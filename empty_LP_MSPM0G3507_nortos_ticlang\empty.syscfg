/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.20.0+3587"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const GPIO4   = GPIO.addInstance();
const GPIO5   = GPIO.addInstance();
const GPIO6   = GPIO.addInstance();
const PWM     = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1    = PWM.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 2;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const gate7  = system.clockTree["MFCLKGATE"];
gate7.enable = true;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 5;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL2X";

GPIO1.$name                         = "KEY";
GPIO1.associatedPins[0].$name       = "key";
GPIO1.associatedPins[0].direction   = "INPUT";
GPIO1.associatedPins[0].pin.$assign = "PA18";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                         = "LED";
GPIO2.associatedPins[0].$name       = "led";
GPIO2.associatedPins[0].pin.$assign = "PB9";

GPIO3.port                          = "PORTA";
GPIO3.$name                         = "BIN";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name       = "BIN1";
GPIO3.associatedPins[0].pin.$assign = "PA16";
GPIO3.associatedPins[1].$name       = "BIN2";
GPIO3.associatedPins[1].pin.$assign = "PA17";

GPIO4.port                          = "PORTA";
GPIO4.$name                         = "AIN";
GPIO4.associatedPins.create(2);
GPIO4.associatedPins[0].$name       = "AIN1";
GPIO4.associatedPins[0].pin.$assign = "PA14";
GPIO4.associatedPins[1].$name       = "AIN2";
GPIO4.associatedPins[1].pin.$assign = "PA13";

GPIO5.$name                               = "ENCODERA";
GPIO5.associatedPins.create(2);
GPIO5.associatedPins[0].$name             = "E1A";
GPIO5.associatedPins[0].direction         = "INPUT";
GPIO5.associatedPins[0].polarity          = "RISE";
GPIO5.associatedPins[0].interruptPriority = "0";
GPIO5.associatedPins[0].interruptEn       = true;
GPIO5.associatedPins[0].pin.$assign       = "PA25";
GPIO5.associatedPins[1].$name             = "E1B";
GPIO5.associatedPins[1].direction         = "INPUT";
GPIO5.associatedPins[1].polarity          = "RISE";
GPIO5.associatedPins[1].interruptPriority = "0";
GPIO5.associatedPins[1].interruptEn       = true;
GPIO5.associatedPins[1].pin.$assign       = "PA26";

GPIO6.$name                               = "ENCODERB";
GPIO6.associatedPins.create(2);
GPIO6.associatedPins[0].$name             = "E2A";
GPIO6.associatedPins[0].direction         = "INPUT";
GPIO6.associatedPins[0].polarity          = "RISE";
GPIO6.associatedPins[0].interruptPriority = "0";
GPIO6.associatedPins[0].interruptEn       = true;
GPIO6.associatedPins[0].pin.$assign       = "PB20";
GPIO6.associatedPins[1].$name             = "E2B";
GPIO6.associatedPins[1].direction         = "INPUT";
GPIO6.associatedPins[1].polarity          = "RISE";
GPIO6.associatedPins[1].interruptPriority = "0";
GPIO6.associatedPins[1].interruptEn       = true;
GPIO6.associatedPins[1].pin.$assign       = "PB24";

PWM1.$name                      = "PWM_0";
PWM1.timerStartTimer            = true;
PWM1.pwmMode                    = "EDGE_ALIGN_UP";
PWM1.timerCount                 = 8000;
PWM1.peripheral.$assign         = "TIMA1";
PWM1.peripheral.ccp0Pin.$assign = "PB2";
PWM1.peripheral.ccp1Pin.$assign = "PB3";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name        = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM1.ccp1PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric5";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

SYSTICK.periodEnable  = true;
SYSTICK.systickEnable = true;
SYSTICK.period        = 16777216;

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkPrescale   = 32;
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerStartTimer    = true;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerPeriod        = "10 ms";
TIMER1.peripheral.$assign = "TIMG0";

UART1.$name                    = "UART_0";
UART1.profile                  = "CONFIG_PROFILE_1";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
UART1.peripheral.$suggestSolution          = "UART0";
