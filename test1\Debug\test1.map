******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Wed Jul 30 11:19:23 2025

OUTPUT FILE NAME:   <test1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000169d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000018c0  0001e740  R  X
  SRAM                  20200000   00008000  000003bf  00007c41  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000018c0   000018c0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001780   00001780    r-x .text
  00001840    00001840    00000048   00000048    r-- .rodata
  00001888    00001888    00000038   00000038    r-- .cinit
20200000    20200000    000001bf   00000000    rw-
  20200000    20200000    000001be   00000000    rw- .data
  202001be    202001be    00000001   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001780     
                  000000c0    00000288     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  00000348    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  000005cc    00000234     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00000800    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00000992    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000994    0000018c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00000b20    0000016c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00000c8c    00000144     empty.o (.text.main)
                  00000dd0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00000edc    000000e4                            : muldf3.S.obj (.text.__muldf3)
                  00000fc0    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  0000105a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000105c    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000010e0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000115c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000011d0    00000068                            : comparedf2.c.obj (.text.__ledf2)
                  00001238    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000129a    00000062     libc.a : memset16.S.obj (.text:memset)
                  000012fc    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00001346    00000002     --HOLE-- [fill = 0]
                  00001348    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001390    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000013d8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00001418    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001458    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001498    0000003c     bsp_usart.o (.text.UART0_IRQHandler)
                  000014d4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001510    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  0000154a    00000002     --HOLE-- [fill = 0]
                  0000154c    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00001584    00000034     bsp_usart.o (.text.uart0_send_string)
                  000015b8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  000015e8    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001618    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001648    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00001674    00000028     systick.o (.text.SysTick_Handler)
                  0000169c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000016c4    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000016e8    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  0000170a    0000001e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00001728    0000001c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001744    0000001c     systick.o (.text.delay_ms)
                  00001760    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00001778    00000018     bsp_usart.o (.text.uart0_init)
                  00001790    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000017a6    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000017b8    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000017ca    00000002     --HOLE-- [fill = 0]
                  000017cc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000017da    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000017e8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000017f4    0000000c     systick.o (.text.get_systicks)
                  00001800    0000000c     Scheduler.o (.text.scheduler_init)
                  0000180c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001816    0000000a     libc.a : sprintf.c.obj (.text._outc)
                  00001820    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001828    00000006     libc.a : exit.c.obj (.text:abort)
                  0000182e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001832    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001836    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000183a    00000006     --HOLE-- [fill = 0]

.cinit     0    00001888    00000038     
                  00001888    00000014     (.cinit..data.load) [load image, compression = lzss]
                  0000189c    0000000c     (__TI_handler_table)
                  000018a8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000018b0    00000010     (__TI_cinit_table)

.rodata    0    00001840    00000048     
                  00001840    00000021     empty.o (.rodata.str1.9517790425240694019.1)
                  00001861    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00001872    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000187c    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00001884    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00001886    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    000001be     UNINITIALIZED
                  20200000    00000100     empty.o (.data.rx_buff)
                  20200100    00000080     bsp_usart.o (.data.uart_rx_buffer)
                  20200180    00000010     empty.o (.data.Anolog)
                  20200190    00000010     empty.o (.data.black)
                  202001a0    00000010     empty.o (.data.white)
                  202001b0    00000008     systick.o (.data.systicks)
                  202001b8    00000004     systick.o (.data.delay_times)
                  202001bc    00000001     bsp_usart.o (.data.uart_rx_index)
                  202001bd    00000001     bsp_usart.o (.data.uart_rx_ticks)

.bss       0    202001be    00000001     UNINITIALIZED
                  202001be    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code   ro data   rw data
       ------                           ----   -------   -------
    .\
       empty.o                          324    33        304    
       ti_msp_dl_config.o               440    20        0      
       startup_mspm0g350x_ticlang.o     6      192       0      
    +--+--------------------------------+------+---------+---------+
       Total:                           770    245       304    
                                                                
    .\app\
       No_Mcu_Ganv_Grayscale_Sensor.o   2002   0         0      
       Scheduler.o                      12     0         1      
    +--+--------------------------------+------+---------+---------+
       Total:                           2014   0         1      
                                                                
    .\bsp\
       bsp_usart.o                      136    0         130    
       systick.o                        80     0         12     
    +--+--------------------------------+------+---------+---------+
       Total:                           216    0         142    
                                                                
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_uart.o                        90     0         0      
       dl_adc12.o                       64     0         0      
       dl_common.o                      10     0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           164    0         0      
                                                                
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   658    17        0      
       memcpy16.S.obj                   154    0         0      
       copy_decompress_lzss.c.obj       124    0         0      
       memset16.S.obj                   98     0         0      
       sprintf.c.obj                    90     0         0      
       autoinit.c.obj                   60     0         0      
       boot_cortex_m.c.obj              40     0         0      
       memccpy.c.obj                    34     0         0      
       copy_zero_init.c.obj             22     0         0      
       copy_decompress_none.c.obj       18     0         0      
       exit.c.obj                       6      0         0      
       pre_init.c.obj                   4      0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           1308   17        0      
                                                                
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4      0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           4      0         0      
                                                                
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     402    0         0      
       divdf3.S.obj                     268    0         0      
       muldf3.S.obj                     228    0         0      
       comparedf2.c.obj                 220    0         0      
       aeabi_dcmp.S.obj                 98     0         0      
       fixdfsi.S.obj                    74     0         0      
       aeabi_uidivmod.S.obj             64     0         0      
       muldsi3.S.obj                    58     0         0      
       floatsidf.S.obj                  44     0         0      
       floatunsidf.S.obj                36     0         0      
       aeabi_memset.S.obj               26     0         0      
       aeabi_memcpy.S.obj               8      0         0      
       aeabi_div0.c.obj                 2      0         0      
    +--+--------------------------------+------+---------+---------+
       Total:                           1528   0         0      
                                                                
       Stack:                           0      0         512    
       Linker Generated:                0      56        0      
    +--+--------------------------------+------+---------+---------+
       Grand Total:                     6004   318       959    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000018b0 records: 2, size/record: 8, table size: 16
	.data: load addr=00001888, load size=00000014 bytes, run addr=20200000, run size=000001be bytes, compression=lzss
	.bss: load addr=000018a8, load size=00000008 bytes, run addr=202001be, run size=00000001 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000189c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                
-------   ----                                
00000993  ADC0_IRQHandler                     
00000993  ADC1_IRQHandler                     
00000993  AES_IRQHandler                      
20200180  Anolog                              
0000182e  C$$EXIT                             
00000993  CANFD0_IRQHandler                   
00000993  DAC0_IRQHandler                     
000013d9  DL_ADC12_setClockConfig             
0000180d  DL_Common_delayCycles               
00001349  DL_UART_init                        
000017a7  DL_UART_setClockConfig              
00000993  DMA_IRQHandler                      
00000993  Default_Handler                     
00000993  GROUP0_IRQHandler                   
00000993  GROUP1_IRQHandler                   
00000b21  Get_Anolog_Value                    
0000182f  HOSTexit                            
00000993  HardFault_Handler                   
00000993  I2C0_IRQHandler                     
00000993  I2C1_IRQHandler                     
00000993  NMI_Handler                         
00000995  No_MCU_Ganv_Sensor_Init             
0000170b  No_MCU_Ganv_Sensor_Init_Frist       
000000c1  No_Mcu_Ganv_Sensor_Task_Without_tick
00000993  PendSV_Handler                      
00000993  RTC_IRQHandler                      
00001833  Reset_Handler                       
00000993  SPI0_IRQHandler                     
00000993  SPI1_IRQHandler                     
00000993  SVC_Handler                         
000015b9  SYSCFG_DL_ADC12_0_init              
00001391  SYSCFG_DL_GPIO_init                 
000015e9  SYSCFG_DL_SYSCTL_init               
00001619  SYSCFG_DL_SYSTICK_init              
0000105d  SYSCFG_DL_UART_0_init               
00001729  SYSCFG_DL_init                      
00001419  SYSCFG_DL_initPower                 
00001675  SysTick_Handler                     
00000993  TIMA0_IRQHandler                    
00000993  TIMA1_IRQHandler                    
00000993  TIMG0_IRQHandler                    
00000993  TIMG12_IRQHandler                   
00000993  TIMG6_IRQHandler                    
00000993  TIMG7_IRQHandler                    
00000993  TIMG8_IRQHandler                    
00001499  UART0_IRQHandler                    
00000993  UART1_IRQHandler                    
00000993  UART2_IRQHandler                    
00000993  UART3_IRQHandler                    
20208000  __STACK_END                         
00000200  __STACK_SIZE                        
00000000  __TI_ATRegion0_region_sz            
00000000  __TI_ATRegion0_src_addr             
00000000  __TI_ATRegion0_trg_addr             
00000000  __TI_ATRegion1_region_sz            
00000000  __TI_ATRegion1_src_addr             
00000000  __TI_ATRegion1_trg_addr             
00000000  __TI_ATRegion2_region_sz            
00000000  __TI_ATRegion2_src_addr             
00000000  __TI_ATRegion2_trg_addr             
000018b0  __TI_CINIT_Base                     
000018c0  __TI_CINIT_Limit                    
000018c0  __TI_CINIT_Warm                     
0000189c  __TI_Handler_Table_Base             
000018a8  __TI_Handler_Table_Limit            
000014d5  __TI_auto_init_nobinit_nopinit      
000010e1  __TI_decompress_lzss                
000017b9  __TI_decompress_none                
ffffffff  __TI_pprof_out_hndl                 
00000349  __TI_printfi_minimal                
ffffffff  __TI_prof_data_size                 
ffffffff  __TI_prof_data_start                
00000000  __TI_static_base__                  
00001791  __TI_zero_init_nomemset             
0000080b  __adddf3                            
000012fd  __aeabi_d2iz                        
0000080b  __aeabi_dadd                        
00001239  __aeabi_dcmpeq                      
00001275  __aeabi_dcmpge                      
00001289  __aeabi_dcmpgt                      
00001261  __aeabi_dcmple                      
0000124d  __aeabi_dcmplt                      
00000dd1  __aeabi_ddiv                        
00000edd  __aeabi_dmul                        
00000801  __aeabi_dsub                        
00001649  __aeabi_i2d                         
0000105b  __aeabi_idiv0                       
000017e9  __aeabi_memclr                      
000017e9  __aeabi_memclr4                     
000017e9  __aeabi_memclr8                     
00001821  __aeabi_memcpy                      
00001821  __aeabi_memcpy4                     
00001821  __aeabi_memcpy8                     
000017cd  __aeabi_memset                      
000017cd  __aeabi_memset4                     
000017cd  __aeabi_memset8                     
000016c5  __aeabi_ui2d                        
00001459  __aeabi_uidiv                       
00001459  __aeabi_uidivmod                    
ffffffff  __binit__                           
000011d1  __cmpdf2                            
00000dd1  __divdf3                            
000011d1  __eqdf2                             
000012fd  __fixdfsi                           
00001649  __floatsidf                         
000016c5  __floatunsidf                       
0000115d  __gedf2                             
0000115d  __gtdf2                             
000011d1  __ledf2                             
000011d1  __ltdf2                             
UNDEFED   __mpu_init                          
00000edd  __muldf3                            
00001511  __muldsi3                           
000011d1  __nedf2                             
20207e00  __stack                             
20200000  __start___llvm_prf_bits             
20200000  __start___llvm_prf_cnts             
20200000  __stop___llvm_prf_bits              
20200000  __stop___llvm_prf_cnts              
00000801  __subdf3                            
0000169d  _c_int00_noargs                     
UNDEFED   _system_post_cinit                  
00001837  _system_pre_init                    
00001829  abort                               
ffffffff  binit                               
20200190  black                               
00001745  delay_ms                            
202001b8  delay_times                         
000017f5  get_systicks                        
00000000  interruptVectors                    
00000c8d  main                                
000016e9  memccpy                             
00000fc1  memcpy                              
0000129b  memset                              
000005cd  normalizeAnalogValues               
20200000  rx_buff                             
00001801  scheduler_init                      
0000154d  sprintf                             
202001be  task_num                            
00001779  uart0_init                          
00001585  uart0_send_string                   
20200100  uart_rx_buffer                      
202001bc  uart_rx_index                       
202001bd  uart_rx_ticks                       
202001a0  white                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                
-------   ----                                
00000000  __TI_ATRegion0_region_sz            
00000000  __TI_ATRegion0_src_addr             
00000000  __TI_ATRegion0_trg_addr             
00000000  __TI_ATRegion1_region_sz            
00000000  __TI_ATRegion1_src_addr             
00000000  __TI_ATRegion1_trg_addr             
00000000  __TI_ATRegion2_region_sz            
00000000  __TI_ATRegion2_src_addr             
00000000  __TI_ATRegion2_trg_addr             
00000000  __TI_static_base__                  
00000000  interruptVectors                    
000000c1  No_Mcu_Ganv_Sensor_Task_Without_tick
00000200  __STACK_SIZE                        
00000349  __TI_printfi_minimal                
000005cd  normalizeAnalogValues               
00000801  __aeabi_dsub                        
00000801  __subdf3                            
0000080b  __adddf3                            
0000080b  __aeabi_dadd                        
00000993  ADC0_IRQHandler                     
00000993  ADC1_IRQHandler                     
00000993  AES_IRQHandler                      
00000993  CANFD0_IRQHandler                   
00000993  DAC0_IRQHandler                     
00000993  DMA_IRQHandler                      
00000993  Default_Handler                     
00000993  GROUP0_IRQHandler                   
00000993  GROUP1_IRQHandler                   
00000993  HardFault_Handler                   
00000993  I2C0_IRQHandler                     
00000993  I2C1_IRQHandler                     
00000993  NMI_Handler                         
00000993  PendSV_Handler                      
00000993  RTC_IRQHandler                      
00000993  SPI0_IRQHandler                     
00000993  SPI1_IRQHandler                     
00000993  SVC_Handler                         
00000993  TIMA0_IRQHandler                    
00000993  TIMA1_IRQHandler                    
00000993  TIMG0_IRQHandler                    
00000993  TIMG12_IRQHandler                   
00000993  TIMG6_IRQHandler                    
00000993  TIMG7_IRQHandler                    
00000993  TIMG8_IRQHandler                    
00000993  UART1_IRQHandler                    
00000993  UART2_IRQHandler                    
00000993  UART3_IRQHandler                    
00000995  No_MCU_Ganv_Sensor_Init             
00000b21  Get_Anolog_Value                    
00000c8d  main                                
00000dd1  __aeabi_ddiv                        
00000dd1  __divdf3                            
00000edd  __aeabi_dmul                        
00000edd  __muldf3                            
00000fc1  memcpy                              
0000105b  __aeabi_idiv0                       
0000105d  SYSCFG_DL_UART_0_init               
000010e1  __TI_decompress_lzss                
0000115d  __gedf2                             
0000115d  __gtdf2                             
000011d1  __cmpdf2                            
000011d1  __eqdf2                             
000011d1  __ledf2                             
000011d1  __ltdf2                             
000011d1  __nedf2                             
00001239  __aeabi_dcmpeq                      
0000124d  __aeabi_dcmplt                      
00001261  __aeabi_dcmple                      
00001275  __aeabi_dcmpge                      
00001289  __aeabi_dcmpgt                      
0000129b  memset                              
000012fd  __aeabi_d2iz                        
000012fd  __fixdfsi                           
00001349  DL_UART_init                        
00001391  SYSCFG_DL_GPIO_init                 
000013d9  DL_ADC12_setClockConfig             
00001419  SYSCFG_DL_initPower                 
00001459  __aeabi_uidiv                       
00001459  __aeabi_uidivmod                    
00001499  UART0_IRQHandler                    
000014d5  __TI_auto_init_nobinit_nopinit      
00001511  __muldsi3                           
0000154d  sprintf                             
00001585  uart0_send_string                   
000015b9  SYSCFG_DL_ADC12_0_init              
000015e9  SYSCFG_DL_SYSCTL_init               
00001619  SYSCFG_DL_SYSTICK_init              
00001649  __aeabi_i2d                         
00001649  __floatsidf                         
00001675  SysTick_Handler                     
0000169d  _c_int00_noargs                     
000016c5  __aeabi_ui2d                        
000016c5  __floatunsidf                       
000016e9  memccpy                             
0000170b  No_MCU_Ganv_Sensor_Init_Frist       
00001729  SYSCFG_DL_init                      
00001745  delay_ms                            
00001779  uart0_init                          
00001791  __TI_zero_init_nomemset             
000017a7  DL_UART_setClockConfig              
000017b9  __TI_decompress_none                
000017cd  __aeabi_memset                      
000017cd  __aeabi_memset4                     
000017cd  __aeabi_memset8                     
000017e9  __aeabi_memclr                      
000017e9  __aeabi_memclr4                     
000017e9  __aeabi_memclr8                     
000017f5  get_systicks                        
00001801  scheduler_init                      
0000180d  DL_Common_delayCycles               
00001821  __aeabi_memcpy                      
00001821  __aeabi_memcpy4                     
00001821  __aeabi_memcpy8                     
00001829  abort                               
0000182e  C$$EXIT                             
0000182f  HOSTexit                            
00001833  Reset_Handler                       
00001837  _system_pre_init                    
0000189c  __TI_Handler_Table_Base             
000018a8  __TI_Handler_Table_Limit            
000018b0  __TI_CINIT_Base                     
000018c0  __TI_CINIT_Limit                    
000018c0  __TI_CINIT_Warm                     
20200000  __start___llvm_prf_bits             
20200000  __start___llvm_prf_cnts             
20200000  __stop___llvm_prf_bits              
20200000  __stop___llvm_prf_cnts              
20200000  rx_buff                             
20200100  uart_rx_buffer                      
20200180  Anolog                              
20200190  black                               
202001a0  white                               
202001b8  delay_times                         
202001bc  uart_rx_index                       
202001bd  uart_rx_ticks                       
202001be  task_num                            
20207e00  __stack                             
20208000  __STACK_END                         
ffffffff  __TI_pprof_out_hndl                 
ffffffff  __TI_prof_data_size                 
ffffffff  __TI_prof_data_start                
ffffffff  __binit__                           
ffffffff  binit                               
UNDEFED   __mpu_init                          
UNDEFED   _system_post_cinit                  

[145 symbols]
