#include "ti_msp_dl_config.h"
#include "bsp_system.h"

/********************************************No_Mcu_Demo*******************************************/
/*****************芯片型号 MSPM0G3507 主频80Mhz ***************************************************/
/*****************引脚 AD0:PB0 AD1:PB1 AD2:PB2  ***************************************************/
/*****************OUT PA27*************************************************************************/
/********************************************No_Mcu_Demo*******************************************/

unsigned short Anolog[8]={0};
unsigned short white[8]={1800,1800,1800,1800,1800,1800,1800,1800};
unsigned short black[8]={300,300,300,300,300,300,300,300};
unsigned short Normal[8];
unsigned char rx_buff[256]={0};

int main(void)
{

	//初始化
	No_MCU_Sensor sensor;
	unsigned char Digtal;

    SYSCFG_DL_init();

    
    DL_ADC12_initSingleSample(ADC12_0_INST,
        DL_ADC12_REPEAT_MODE_ENABLED, DL_ADC12_SAMPLING_SOURCE_AUTO, DL_ADC12_TRIG_SRC_SOFTWARE,
        DL_ADC12_SAMP_CONV_RES_12_BIT, DL_ADC12_SAMP_CONV_DATA_FORMAT_UNSIGNED);
    //初始化传感器，不带黑白值
	No_MCU_Ganv_Sensor_Init_Frist(&sensor);
	No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
	Get_Anolog_Value(&sensor,Anolog);

	//也可以自己写按键逻辑完成一键校准功能
	sprintf((char *)rx_buff,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
	uart0_send_string((char *)rx_buff);
	//delay_ms(100);
	memset(rx_buff,0,256);
	//得到黑白校准值之后，初始化传感器
	No_MCU_Ganv_Sensor_Init(&sensor,white,black);

    uart0_init();
    
    scheduler_init();
	delay_ms(100);
    while (1)
    {
        //无时基传感器常规任务，包含模拟量，数字量，归一化量
		No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);


        //数值小的是黑色 
        //获取传感器模拟量结果(有黑白值初始化后返回1 没有返回 0)
        if(Get_Anolog_Value(&sensor,Anolog)){
        sprintf((char *)rx_buff,"Anolog %d-%d-%d-%d-%d-%d-%d-%d\r\n",Anolog[0],Anolog[1],Anolog[2],Anolog[3],Anolog[4],Anolog[5],Anolog[6],Anolog[7]);
        uart0_send_string((char *)rx_buff);
        memset(rx_buff,0,256);
        }

        // //scheduler_run();
        delay_ms(1000);
    }
}

