<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iD:/ti/mspm0_sdk_2_01_00_03/source -iC:/Users/<USER>/Desktop/empty_LP_MSPM0G3507_nortos_ticlang -iC:/Users/<USER>/Desktop/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./Hardware/board.o ./Hardware/encoder.o ./Hardware/key.o ./Hardware/led.o ./Hardware/motor.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x6886df69</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1615</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>board.o</file>
         <name>board.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>encoder.o</file>
         <name>encoder.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>key.o</file>
         <name>key.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>led.o</file>
         <name>led.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\Hardware\</path>
         <kind>object</kind>
         <file>motor.o</file>
         <name>motor.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\Users\<USER>\Desktop\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>D:\ti\mspm0_sdk_2_01_00_03\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>printf.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-e9">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-ea">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-eb">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-ec">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-ed">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-ee">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-ef">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-f0">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-f1">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-f2">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-f3">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-f4">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-f5">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-f6">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-f7">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-f8">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-f9">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-fa">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-fb">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-fc">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-172">
         <name>.text:__TI_printfi_minimal</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x284</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.click_N_Double</name>
         <load_address>0x344</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x344</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.GROUP1_IRQHandler</name>
         <load_address>0x470</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x470</run_address>
         <size>0x120</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text.Set_PWM</name>
         <load_address>0x590</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x590</run_address>
         <size>0x10c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x69c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x69c</run_address>
         <size>0xec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x788</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x788</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-149">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x870</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x870</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text</name>
         <load_address>0x94c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x94c</run_address>
         <size>0xd8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f4"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.TIMG0_IRQHandler</name>
         <load_address>0xa24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa24</run_address>
         <size>0xd0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-194">
         <name>.text.DL_Timer_initPWMMode</name>
         <load_address>0xaf4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaf4</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-74">
         <name>.text.Velocity_A</name>
         <load_address>0xbb8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbb8</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text.Velocity_B</name>
         <load_address>0xc7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc7c</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text.__mulsf3</name>
         <load_address>0xd40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd40</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f8"/>
      </object_component>
      <object_component id="oc-152">
         <name>.text.__NVIC_SetPriority</name>
         <load_address>0xdcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdcc</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-155">
         <name>.text.DL_TimerA_initPWMMode</name>
         <load_address>0xe50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe50</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-111">
         <name>.text.SYSCFG_DL_PWM_0_init</name>
         <load_address>0xed0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xed0</run_address>
         <size>0x80</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0xf50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf50</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xfcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfcc</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text.main</name>
         <load_address>0x1034</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1034</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x1094</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1094</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.printf</name>
         <load_address>0x10f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10f0</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-183">
         <name>.text.fputs</name>
         <load_address>0x114c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x114c</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-168">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x119c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x119c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x11e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11e8</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-166">
         <name>.text.DL_UART_init</name>
         <load_address>0x1234</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1234</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x127c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x127c</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.text.LED_Flash</name>
         <load_address>0x12c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12c0</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-19e">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x1304</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1304</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text.__floatsisf</name>
         <load_address>0x1344</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1344</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1380</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1380</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.__muldsi3</name>
         <load_address>0x13bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13bc</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-1c9">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x13f6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13f6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.__fixsfsi</name>
         <load_address>0x13f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13f8</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-fb"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x1430</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1430</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.text.limit_PWM</name>
         <load_address>0x1464</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1464</run_address>
         <size>0x34</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.text.Key</name>
         <load_address>0x1498</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1498</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x14c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14c4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.__NVIC_ClearPendingIRQ</name>
         <load_address>0x14f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14f0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x151c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x151c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-188">
         <name>.text.fputc</name>
         <load_address>0x1548</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1548</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-191">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1574</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1574</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-170">
         <name>.text.DL_SYSTICK_init</name>
         <load_address>0x159c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x159c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.text.DL_UART_setRXFIFOThreshold</name>
         <load_address>0x15c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15c4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.text.DL_UART_setTXFIFOThreshold</name>
         <load_address>0x15ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15ec</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-59">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1614</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1614</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-199">
         <name>.text.memccpy</name>
         <load_address>0x163c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x163c</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.text._nop</name>
         <load_address>0x165e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x165e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1660</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1660</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-145">
         <name>.text.DL_SYSCTL_setFlashWaitState</name>
         <load_address>0x1680</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1680</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-167">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x16a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16a0</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-62">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x16c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16c0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-142">
         <name>.text.DL_GPIO_clearInterruptStatus</name>
         <load_address>0x16dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16dc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-143">
         <name>.text.DL_GPIO_enableInterrupt</name>
         <load_address>0x16f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16f8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.DL_GPIO_initDigitalInput</name>
         <load_address>0x1714</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1714</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1730</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1730</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x174c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x174c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x1768</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1768</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.text.DL_Timer_enableInterrupt</name>
         <load_address>0x1784</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1784</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-157">
         <name>.text.DL_Timer_setCaptCompUpdateMethod</name>
         <load_address>0x17a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17a0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-154">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x17bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17bc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x17d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17d8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x17f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17f0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text.DL_GPIO_getEnabledInterruptStatus</name>
         <load_address>0x1808</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1808</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x1820</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1820</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-131">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1838</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1838</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x1850</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1850</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.DL_GPIO_setUpperPinsPolarity</name>
         <load_address>0x1868</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1868</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.DL_GPIO_togglePins</name>
         <load_address>0x1880</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1880</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-144">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x1898</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1898</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-135">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x18b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18b0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x18c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18c8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-156">
         <name>.text.DL_Timer_setCaptureCompareOutCtl</name>
         <load_address>0x18e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18e0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x18f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18f8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-169">
         <name>.text.DL_UART_enableFIFOs</name>
         <load_address>0x1910</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1910</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-136">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1928</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1928</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.text.DL_UART_isBusy</name>
         <load_address>0x1940</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1940</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1958</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1958</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-61">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1970</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1970</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1986</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1986</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.text.DL_UART_enable</name>
         <load_address>0x199c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x199c</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.text.DL_UART_enableLoopbackMode</name>
         <load_address>0x19b2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19b2</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c3">
         <name>.text.DL_UART_transmitData</name>
         <load_address>0x19c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19c8</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-50">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x19de</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19de</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x19f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19f4</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1a08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a08</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1a1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a1c</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x1a30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a30</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-150">
         <name>.text.DL_SYSCTL_enableMFCLK</name>
         <load_address>0x1a44</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a44</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-158">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x1a58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a58</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-159">
         <name>.text.DL_Timer_setCCPDirection</name>
         <load_address>0x1a6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a6c</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.text.LED_ON</name>
         <load_address>0x1a80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a80</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text.LED_Toggle</name>
         <load_address>0x1a94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a94</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-69">
         <name>.text.DL_Timer_getPendingInterrupt</name>
         <load_address>0x1aa8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1aa8</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x1aba</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1aba</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x1acc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1acc</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x1ade</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ade</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x1af0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1af0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-171">
         <name>.text.DL_SYSTICK_enable</name>
         <load_address>0x1b00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b00</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.DL_Timer_setCaptureCompareValue</name>
         <load_address>0x1b10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b10</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0x1b20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b20</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-198">
         <name>.text.__aeabi_memset</name>
         <load_address>0x1b30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b30</run_address>
         <size>0xe</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-197">
         <name>.text.strlen</name>
         <load_address>0x1b3e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b3e</run_address>
         <size>0xe</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text:TI_memset_small</name>
         <load_address>0x1b4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b4c</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-137">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1b5a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b5a</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-49">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x1b64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b64</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-101"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text._outc</name>
         <load_address>0x1b6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b6c</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text._outs</name>
         <load_address>0x1b74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b74</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text:abort</name>
         <load_address>0x1b7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b7c</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x1b82</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b82</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.HOSTexit</name>
         <load_address>0x1b86</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b86</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1b8a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b8a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.text._system_pre_init</name>
         <load_address>0x1b8e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b8e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-271">
         <name>.cinit..data.load</name>
         <load_address>0x1c10</load_address>
         <readonly>true</readonly>
         <run_address>0x1c10</run_address>
         <size>0x36</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-26f">
         <name>__TI_handler_table</name>
         <load_address>0x1c48</load_address>
         <readonly>true</readonly>
         <run_address>0x1c48</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-272">
         <name>.cinit..bss.load</name>
         <load_address>0x1c54</load_address>
         <readonly>true</readonly>
         <run_address>0x1c54</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-270">
         <name>__TI_cinit_table</name>
         <load_address>0x1c5c</load_address>
         <readonly>true</readonly>
         <run_address>0x1c5c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-153">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x1b98</load_address>
         <readonly>true</readonly>
         <run_address>0x1b98</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x1bc0</load_address>
         <readonly>true</readonly>
         <run_address>0x1bc0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19d">
         <name>.rodata.str1.15363888844622738466.1</name>
         <load_address>0x1bd4</load_address>
         <readonly>true</readonly>
         <run_address>0x1bd4</run_address>
         <size>0x11</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x1be6</load_address>
         <readonly>true</readonly>
         <run_address>0x1be6</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-db">
         <name>.rodata.str1.9517790425240694019.1</name>
         <load_address>0x1bf0</load_address>
         <readonly>true</readonly>
         <run_address>0x1bf0</run_address>
         <size>0x9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-15a">
         <name>.rodata.gPWM_0ClockConfig</name>
         <load_address>0x1bf9</load_address>
         <readonly>true</readonly>
         <run_address>0x1bf9</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.rodata.gPWM_0Config</name>
         <load_address>0x1bfc</load_address>
         <readonly>true</readonly>
         <run_address>0x1bfc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x1c04</load_address>
         <readonly>true</readonly>
         <run_address>0x1c04</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x1c07</load_address>
         <readonly>true</readonly>
         <run_address>0x1c07</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-237">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-7e">
         <name>.data.Flag_Stop</name>
         <load_address>0x202000f0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000f0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.data.click_N_Double.double_key</name>
         <load_address>0x20200104</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200104</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.data.Velcity_Kp</name>
         <load_address>0x202000f8</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000f8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.data.Velcity_Ki</name>
         <load_address>0x202000f4</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000f4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-119">
         <name>.data._lock</name>
         <load_address>0x202000fc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202000fc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.data._unlock</name>
         <load_address>0x20200100</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200100</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.data._ftable</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0xf0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.bss.click_N_Double.flag_key</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001fb</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.bss.click_N_Double.count_key</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001fa</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.bss.click_N_Double.count_single</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001f8</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.bss.click_N_Double.Forever_count</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001f6</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.bss.LED_Flash.temp</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001f4</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.bss.Velocity_A.ControlVelocityA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.bss.Velocity_A.Last_biasA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001c8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.bss.Velocity_B.ControlVelocityB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001cc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.bss.Velocity_B.Last_biasB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001d0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.common:encoderA_cnt</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001e4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-7d">
         <name>.common:encoderB_cnt</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001e8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-7f">
         <name>.common:PWMA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001dc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-80">
         <name>.common:PWMB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001e0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-115">
         <name>.common:gPWM_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200108</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-63">
         <name>.common:gpio_interrup1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001ec</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-64">
         <name>.common:gpio_interrup2</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001f0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-65">
         <name>.common:Get_Encoder_countA</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001d4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-66">
         <name>.common:Get_Encoder_countB</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202001d8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-274">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x196</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_abbrev</name>
         <load_address>0x196</load_address>
         <run_address>0x196</run_address>
         <size>0x226</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_abbrev</name>
         <load_address>0x3bc</load_address>
         <run_address>0x3bc</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c4">
         <name>.debug_abbrev</name>
         <load_address>0x429</load_address>
         <run_address>0x429</run_address>
         <size>0x1c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_abbrev</name>
         <load_address>0x5ed</load_address>
         <run_address>0x5ed</run_address>
         <size>0x10b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_abbrev</name>
         <load_address>0x6f8</load_address>
         <run_address>0x6f8</run_address>
         <size>0x12b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_abbrev</name>
         <load_address>0x823</load_address>
         <run_address>0x823</run_address>
         <size>0x100</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_abbrev</name>
         <load_address>0x923</load_address>
         <run_address>0x923</run_address>
         <size>0x150</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.debug_abbrev</name>
         <load_address>0xa73</load_address>
         <run_address>0xa73</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_abbrev</name>
         <load_address>0xad5</load_address>
         <run_address>0xad5</run_address>
         <size>0x258</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-195">
         <name>.debug_abbrev</name>
         <load_address>0xd2d</load_address>
         <run_address>0xd2d</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_abbrev</name>
         <load_address>0xfac</load_address>
         <run_address>0xfac</run_address>
         <size>0x259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_abbrev</name>
         <load_address>0x1205</load_address>
         <run_address>0x1205</run_address>
         <size>0x102</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-1a2">
         <name>.debug_abbrev</name>
         <load_address>0x1307</load_address>
         <run_address>0x1307</run_address>
         <size>0x2a3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-17e">
         <name>.debug_abbrev</name>
         <load_address>0x15aa</load_address>
         <run_address>0x15aa</run_address>
         <size>0x73</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-180">
         <name>.debug_abbrev</name>
         <load_address>0x161d</load_address>
         <run_address>0x161d</run_address>
         <size>0x77</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-1c6">
         <name>.debug_abbrev</name>
         <load_address>0x1694</load_address>
         <run_address>0x1694</run_address>
         <size>0x8b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0x171f</load_address>
         <run_address>0x171f</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_abbrev</name>
         <load_address>0x17ce</load_address>
         <run_address>0x17ce</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_abbrev</name>
         <load_address>0x193e</load_address>
         <run_address>0x193e</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_abbrev</name>
         <load_address>0x1977</load_address>
         <run_address>0x1977</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_abbrev</name>
         <load_address>0x1a39</load_address>
         <run_address>0x1a39</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_abbrev</name>
         <load_address>0x1aa9</load_address>
         <run_address>0x1aa9</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_abbrev</name>
         <load_address>0x1b36</load_address>
         <run_address>0x1b36</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-189">
         <name>.debug_abbrev</name>
         <load_address>0x1bce</load_address>
         <run_address>0x1bce</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_abbrev</name>
         <load_address>0x1bfa</load_address>
         <run_address>0x1bfa</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f4"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_abbrev</name>
         <load_address>0x1c21</load_address>
         <run_address>0x1c21</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_abbrev</name>
         <load_address>0x1c48</load_address>
         <run_address>0x1c48</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f8"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_abbrev</name>
         <load_address>0x1c6f</load_address>
         <run_address>0x1c6f</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fb"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_abbrev</name>
         <load_address>0x1c96</load_address>
         <run_address>0x1c96</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_abbrev</name>
         <load_address>0x1cbd</load_address>
         <run_address>0x1cbd</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-101"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_abbrev</name>
         <load_address>0x1ce4</load_address>
         <run_address>0x1ce4</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-1ce">
         <name>.debug_abbrev</name>
         <load_address>0x1d09</load_address>
         <run_address>0x1d09</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-20f">
         <name>.debug_abbrev</name>
         <load_address>0x1d30</load_address>
         <run_address>0x1d30</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_abbrev</name>
         <load_address>0x1d89</load_address>
         <run_address>0x1d89</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_abbrev</name>
         <load_address>0x1dae</load_address>
         <run_address>0x1dae</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-276">
         <name>.debug_abbrev</name>
         <load_address>0x1dd3</load_address>
         <run_address>0x1dd3</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xaef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_info</name>
         <load_address>0xaef</load_address>
         <run_address>0xaef</run_address>
         <size>0x3db5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x48a4</load_address>
         <run_address>0x48a4</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-184">
         <name>.debug_info</name>
         <load_address>0x4924</load_address>
         <run_address>0x4924</run_address>
         <size>0xb71</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x5495</load_address>
         <run_address>0x5495</run_address>
         <size>0x805</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_info</name>
         <load_address>0x5c9a</load_address>
         <run_address>0x5c9a</run_address>
         <size>0x845</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_info</name>
         <load_address>0x64df</load_address>
         <run_address>0x64df</run_address>
         <size>0x7ff</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_info</name>
         <load_address>0x6cde</load_address>
         <run_address>0x6cde</run_address>
         <size>0xf05</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_info</name>
         <load_address>0x7be3</load_address>
         <run_address>0x7be3</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_info</name>
         <load_address>0x7c58</load_address>
         <run_address>0x7c58</run_address>
         <size>0x2f7d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_info</name>
         <load_address>0xabd5</load_address>
         <run_address>0xabd5</run_address>
         <size>0x1259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.debug_info</name>
         <load_address>0xbe2e</load_address>
         <run_address>0xbe2e</run_address>
         <size>0x1f76</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_info</name>
         <load_address>0xdda4</load_address>
         <run_address>0xdda4</run_address>
         <size>0x1e4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-174">
         <name>.debug_info</name>
         <load_address>0xdf88</load_address>
         <run_address>0xdf88</run_address>
         <size>0x1f24</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_info</name>
         <load_address>0xfeac</load_address>
         <run_address>0xfeac</run_address>
         <size>0xaa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_info</name>
         <load_address>0xff56</load_address>
         <run_address>0xff56</run_address>
         <size>0x132</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-19b">
         <name>.debug_info</name>
         <load_address>0x10088</load_address>
         <run_address>0x10088</run_address>
         <size>0xce</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x10156</load_address>
         <run_address>0x10156</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_info</name>
         <load_address>0x10579</load_address>
         <run_address>0x10579</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_info</name>
         <load_address>0x10cbd</load_address>
         <run_address>0x10cbd</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_info</name>
         <load_address>0x10d03</load_address>
         <run_address>0x10d03</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0x10e95</load_address>
         <run_address>0x10e95</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x10f5b</load_address>
         <run_address>0x10f5b</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_info</name>
         <load_address>0x110d7</load_address>
         <run_address>0x110d7</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-124">
         <name>.debug_info</name>
         <load_address>0x111cf</load_address>
         <run_address>0x111cf</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_info</name>
         <load_address>0x1120a</load_address>
         <run_address>0x1120a</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f4"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_info</name>
         <load_address>0x113b1</load_address>
         <run_address>0x113b1</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_info</name>
         <load_address>0x11540</load_address>
         <run_address>0x11540</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f8"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_info</name>
         <load_address>0x116cd</load_address>
         <run_address>0x116cd</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fb"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_info</name>
         <load_address>0x1185c</load_address>
         <run_address>0x1185c</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_info</name>
         <load_address>0x119ef</load_address>
         <run_address>0x119ef</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-101"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_info</name>
         <load_address>0x11b88</load_address>
         <run_address>0x11b88</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-1a1">
         <name>.debug_info</name>
         <load_address>0x11d3d</load_address>
         <run_address>0x11d3d</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-1ca">
         <name>.debug_info</name>
         <load_address>0x11ef9</load_address>
         <run_address>0x11ef9</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_info</name>
         <load_address>0x11f7e</load_address>
         <run_address>0x11f7e</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_info</name>
         <load_address>0x12278</load_address>
         <run_address>0x12278</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-275">
         <name>.debug_info</name>
         <load_address>0x124bc</load_address>
         <run_address>0x124bc</run_address>
         <size>0xb2</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_ranges</name>
         <load_address>0x38</load_address>
         <run_address>0x38</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x1b0</load_address>
         <run_address>0x1b0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-185">
         <name>.debug_ranges</name>
         <load_address>0x1c8</load_address>
         <run_address>0x1c8</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_ranges</name>
         <load_address>0x238</load_address>
         <run_address>0x238</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_ranges</name>
         <load_address>0x260</load_address>
         <run_address>0x260</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_ranges</name>
         <load_address>0x288</load_address>
         <run_address>0x288</run_address>
         <size>0x40</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_ranges</name>
         <load_address>0x2c8</load_address>
         <run_address>0x2c8</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_ranges</name>
         <load_address>0x300</load_address>
         <run_address>0x300</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-165">
         <name>.debug_ranges</name>
         <load_address>0x470</load_address>
         <run_address>0x470</run_address>
         <size>0x190</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.debug_ranges</name>
         <load_address>0x600</load_address>
         <run_address>0x600</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_ranges</name>
         <load_address>0x7a8</load_address>
         <run_address>0x7a8</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_ranges</name>
         <load_address>0x7c8</load_address>
         <run_address>0x7c8</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_ranges</name>
         <load_address>0x940</load_address>
         <run_address>0x940</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x960</load_address>
         <run_address>0x960</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_ranges</name>
         <load_address>0x9a8</load_address>
         <run_address>0x9a8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_ranges</name>
         <load_address>0x9f0</load_address>
         <run_address>0x9f0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_ranges</name>
         <load_address>0xa08</load_address>
         <run_address>0xa08</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_ranges</name>
         <load_address>0xa58</load_address>
         <run_address>0xa58</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_ranges</name>
         <load_address>0xa70</load_address>
         <run_address>0xa70</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-1cb">
         <name>.debug_ranges</name>
         <load_address>0xa98</load_address>
         <run_address>0xa98</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_ranges</name>
         <load_address>0xab0</load_address>
         <run_address>0xab0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_ranges</name>
         <load_address>0xad8</load_address>
         <run_address>0xad8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x8a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_str</name>
         <load_address>0x8a6</load_address>
         <run_address>0x8a6</run_address>
         <size>0x2df9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_str</name>
         <load_address>0x369f</load_address>
         <run_address>0x369f</run_address>
         <size>0x169</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-1c5">
         <name>.debug_str</name>
         <load_address>0x3808</load_address>
         <run_address>0x3808</run_address>
         <size>0x737</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_str</name>
         <load_address>0x3f3f</load_address>
         <run_address>0x3f3f</run_address>
         <size>0x4f4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_str</name>
         <load_address>0x4433</load_address>
         <run_address>0x4433</run_address>
         <size>0x511</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_str</name>
         <load_address>0x4944</load_address>
         <run_address>0x4944</run_address>
         <size>0x4c6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_str</name>
         <load_address>0x4e0a</load_address>
         <run_address>0x4e0a</run_address>
         <size>0x762</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.debug_str</name>
         <load_address>0x556c</load_address>
         <run_address>0x556c</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_str</name>
         <load_address>0x56e3</load_address>
         <run_address>0x56e3</run_address>
         <size>0x1c27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-196">
         <name>.debug_str</name>
         <load_address>0x730a</load_address>
         <run_address>0x730a</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_str</name>
         <load_address>0x7ff7</load_address>
         <run_address>0x7ff7</run_address>
         <size>0x16bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_str</name>
         <load_address>0x96b3</load_address>
         <run_address>0x96b3</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-1a3">
         <name>.debug_str</name>
         <load_address>0x985b</load_address>
         <run_address>0x985b</run_address>
         <size>0x8f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-17f">
         <name>.debug_str</name>
         <load_address>0xa154</load_address>
         <run_address>0xa154</run_address>
         <size>0x11c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-181">
         <name>.debug_str</name>
         <load_address>0xa270</load_address>
         <run_address>0xa270</run_address>
         <size>0x190</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-1c7">
         <name>.debug_str</name>
         <load_address>0xa400</load_address>
         <run_address>0xa400</run_address>
         <size>0x127</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0xa527</load_address>
         <run_address>0xa527</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_str</name>
         <load_address>0xa74c</load_address>
         <run_address>0xa74c</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_str</name>
         <load_address>0xaa7b</load_address>
         <run_address>0xaa7b</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_str</name>
         <load_address>0xab70</load_address>
         <run_address>0xab70</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_str</name>
         <load_address>0xad0b</load_address>
         <run_address>0xad0b</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_str</name>
         <load_address>0xae73</load_address>
         <run_address>0xae73</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-127">
         <name>.debug_str</name>
         <load_address>0xb048</load_address>
         <run_address>0xb048</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.debug_str</name>
         <load_address>0xb190</load_address>
         <run_address>0xb190</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-210">
         <name>.debug_str</name>
         <load_address>0xb279</load_address>
         <run_address>0xb279</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_frame</name>
         <load_address>0x98</load_address>
         <run_address>0x98</run_address>
         <size>0x424</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x4bc</load_address>
         <run_address>0x4bc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-186">
         <name>.debug_frame</name>
         <load_address>0x4ec</load_address>
         <run_address>0x4ec</run_address>
         <size>0x158</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x644</load_address>
         <run_address>0x644</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_frame</name>
         <load_address>0x6ac</load_address>
         <run_address>0x6ac</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0x724</load_address>
         <run_address>0x724</run_address>
         <size>0xb0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_frame</name>
         <load_address>0x7d4</load_address>
         <run_address>0x7d4</run_address>
         <size>0xa0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_frame</name>
         <load_address>0x874</load_address>
         <run_address>0x874</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_frame</name>
         <load_address>0x894</load_address>
         <run_address>0x894</run_address>
         <size>0x400</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_frame</name>
         <load_address>0xc94</load_address>
         <run_address>0xc94</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.debug_frame</name>
         <load_address>0xe4c</load_address>
         <run_address>0xe4c</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_frame</name>
         <load_address>0xf78</load_address>
         <run_address>0xf78</run_address>
         <size>0x5c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_frame</name>
         <load_address>0xfd4</load_address>
         <run_address>0xfd4</run_address>
         <size>0x480</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-17c">
         <name>.debug_frame</name>
         <load_address>0x1454</load_address>
         <run_address>0x1454</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-19c">
         <name>.debug_frame</name>
         <load_address>0x1494</load_address>
         <run_address>0x1494</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_frame</name>
         <load_address>0x14c0</load_address>
         <run_address>0x14c0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_frame</name>
         <load_address>0x1550</load_address>
         <run_address>0x1550</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_frame</name>
         <load_address>0x1650</load_address>
         <run_address>0x1650</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x1670</load_address>
         <run_address>0x1670</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x16a8</load_address>
         <run_address>0x16a8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x16d0</load_address>
         <run_address>0x16d0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_frame</name>
         <load_address>0x1700</load_address>
         <run_address>0x1700</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-123">
         <name>.debug_frame</name>
         <load_address>0x1730</load_address>
         <run_address>0x1730</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-1cd">
         <name>.debug_frame</name>
         <load_address>0x1750</load_address>
         <run_address>0x1750</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_line</name>
         <load_address>0x3a6</load_address>
         <run_address>0x3a6</run_address>
         <size>0xb94</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0xf3a</load_address>
         <run_address>0xf3a</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-187">
         <name>.debug_line</name>
         <load_address>0xff2</load_address>
         <run_address>0xff2</run_address>
         <size>0x56c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x155e</load_address>
         <run_address>0x155e</run_address>
         <size>0x2e6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_line</name>
         <load_address>0x1844</load_address>
         <run_address>0x1844</run_address>
         <size>0x349</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_line</name>
         <load_address>0x1b8d</load_address>
         <run_address>0x1b8d</run_address>
         <size>0x2ab</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_line</name>
         <load_address>0x1e38</load_address>
         <run_address>0x1e38</run_address>
         <size>0x3bd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_line</name>
         <load_address>0x21f5</load_address>
         <run_address>0x21f5</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_line</name>
         <load_address>0x22d9</load_address>
         <run_address>0x22d9</run_address>
         <size>0x15a2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_line</name>
         <load_address>0x387b</load_address>
         <run_address>0x387b</run_address>
         <size>0x989</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.debug_line</name>
         <load_address>0x4204</load_address>
         <run_address>0x4204</run_address>
         <size>0x8e4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_line</name>
         <load_address>0x4ae8</load_address>
         <run_address>0x4ae8</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_line</name>
         <load_address>0x4c77</load_address>
         <run_address>0x4c77</run_address>
         <size>0x1c90</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-17d">
         <name>.debug_line</name>
         <load_address>0x6907</load_address>
         <run_address>0x6907</run_address>
         <size>0x6c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-182">
         <name>.debug_line</name>
         <load_address>0x6973</load_address>
         <run_address>0x6973</run_address>
         <size>0x8f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-19a">
         <name>.debug_line</name>
         <load_address>0x6a02</load_address>
         <run_address>0x6a02</run_address>
         <size>0xcf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x6ad1</load_address>
         <run_address>0x6ad1</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_line</name>
         <load_address>0x6cad</load_address>
         <run_address>0x6cad</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x71c7</load_address>
         <run_address>0x71c7</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0x7205</load_address>
         <run_address>0x7205</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x7303</load_address>
         <run_address>0x7303</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x73c3</load_address>
         <run_address>0x73c3</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_line</name>
         <load_address>0x758b</load_address>
         <run_address>0x758b</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_line</name>
         <load_address>0x75f2</load_address>
         <run_address>0x75f2</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-e9"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_line</name>
         <load_address>0x7633</load_address>
         <run_address>0x7633</run_address>
         <size>0x107</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f4"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_line</name>
         <load_address>0x773a</load_address>
         <run_address>0x773a</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_line</name>
         <load_address>0x77f3</load_address>
         <run_address>0x77f3</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f8"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_line</name>
         <load_address>0x78d3</load_address>
         <run_address>0x78d3</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fb"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_line</name>
         <load_address>0x798b</load_address>
         <run_address>0x798b</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_line</name>
         <load_address>0x7a47</load_address>
         <run_address>0x7a47</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-101"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_line</name>
         <load_address>0x7aeb</load_address>
         <run_address>0x7aeb</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-1a0">
         <name>.debug_line</name>
         <load_address>0x7ba5</load_address>
         <run_address>0x7ba5</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-1cc">
         <name>.debug_line</name>
         <load_address>0x7c67</load_address>
         <run_address>0x7c67</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_line</name>
         <load_address>0x7d1c</load_address>
         <run_address>0x7d1c</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_line</name>
         <load_address>0x7dbc</load_address>
         <run_address>0x7dbc</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
      <object_component id="oc-190">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x18ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_loc</name>
         <load_address>0x18c0</load_address>
         <run_address>0x18c0</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.debug_loc</name>
         <load_address>0x207c</load_address>
         <run_address>0x207c</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_loc</name>
         <load_address>0x2490</load_address>
         <run_address>0x2490</run_address>
         <size>0x10a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-175">
         <name>.debug_loc</name>
         <load_address>0x259a</load_address>
         <run_address>0x259a</run_address>
         <size>0x32d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1c8">
         <name>.debug_loc</name>
         <load_address>0x5872</load_address>
         <run_address>0x5872</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3b"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_loc</name>
         <load_address>0x5931</load_address>
         <run_address>0x5931</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_loc</name>
         <load_address>0x5a09</load_address>
         <run_address>0x5a09</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_loc</name>
         <load_address>0x5e2d</load_address>
         <run_address>0x5e2d</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-42"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x5f99</load_address>
         <run_address>0x5f99</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-43"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_loc</name>
         <load_address>0x6008</load_address>
         <run_address>0x6008</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_loc</name>
         <load_address>0x616f</load_address>
         <run_address>0x616f</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-48"/>
      </object_component>
      <object_component id="oc-211">
         <name>.debug_loc</name>
         <load_address>0x6195</load_address>
         <run_address>0x6195</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-108"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f4"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f7"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-f8"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fb"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-101"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-19f">
         <name>.debug_aranges</name>
         <load_address>0xe8</load_address>
         <run_address>0xe8</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-103"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_aranges</name>
         <load_address>0x108</load_address>
         <run_address>0x108</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-109"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_aranges</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10a"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x1ad8</size>
         <contents>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-194"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-155"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-183"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-19e"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-1c9"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-191"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-199"/>
            <object_component_ref idref="oc-17a"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-157"/>
            <object_component_ref idref="oc-154"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-1c2"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-1c3"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-171"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-198"/>
            <object_component_ref idref="oc-197"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-8c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1c10</load_address>
         <run_address>0x1c10</run_address>
         <size>0x60</size>
         <contents>
            <object_component_ref idref="oc-271"/>
            <object_component_ref idref="oc-26f"/>
            <object_component_ref idref="oc-272"/>
            <object_component_ref idref="oc-270"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x1b98</load_address>
         <run_address>0x1b98</run_address>
         <size>0x78</size>
         <contents>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-19d"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-15b"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-16e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-237"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x105</size>
         <contents>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-11b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200108</run_address>
         <size>0xf4</size>
         <contents>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-66"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-274"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-22e" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-22f" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-230" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-231" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-232" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-233" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-235" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-251" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1de2</size>
         <contents>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-1c4"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-18e"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-195"/>
            <object_component_ref idref="oc-192"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-1a2"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-1c6"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-189"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-1ce"/>
            <object_component_ref idref="oc-20f"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-276"/>
         </contents>
      </logical_group>
      <logical_group id="lg-253" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1256e</size>
         <contents>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-184"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-19b"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-1a1"/>
            <object_component_ref idref="oc-1ca"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-275"/>
         </contents>
      </logical_group>
      <logical_group id="lg-255" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb00</size>
         <contents>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-185"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-17b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-1cb"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-109"/>
         </contents>
      </logical_group>
      <logical_group id="lg-257" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb40c</size>
         <contents>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-1c5"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-196"/>
            <object_component_ref idref="oc-193"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-1a3"/>
            <object_component_ref idref="oc-17f"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-1c7"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-210"/>
         </contents>
      </logical_group>
      <logical_group id="lg-259" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1780</size>
         <contents>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-186"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-17c"/>
            <object_component_ref idref="oc-19c"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-1cd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-25b" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7e3c</size>
         <contents>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-187"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-17d"/>
            <object_component_ref idref="oc-182"/>
            <object_component_ref idref="oc-19a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-1a0"/>
            <object_component_ref idref="oc-1cc"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-10c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-25d" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x61b5</size>
         <contents>
            <object_component_ref idref="oc-190"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-1c8"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-211"/>
         </contents>
      </logical_group>
      <logical_group id="lg-269" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x158</size>
         <contents>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-19f"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-10a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-273" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-27d" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1c70</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-27e" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x1fc</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-27f" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1c70</used_space>
         <unused_space>0x1e390</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x1ad8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1b98</start_address>
               <size>0x78</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1c10</start_address>
               <size>0x60</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1c70</start_address>
               <size>0x1e390</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x3f9</used_space>
         <unused_space>0x7c07</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-233"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-235"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x105</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200105</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x20200108</start_address>
               <size>0xf4</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202001fc</start_address>
               <size>0x7c04</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1c10</load_address>
            <load_size>0x36</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x105</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x1c54</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200108</run_address>
            <run_size>0xf4</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x1c5c</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1c6c</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1c6c</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x1c48</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x1c54</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-4f">
         <name>main</name>
         <value>0x1035</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-50">
         <name>encoderA_cnt</name>
         <value>0x202001e4</value>
      </symbol>
      <symbol id="sm-51">
         <name>encoderB_cnt</name>
         <value>0x202001e8</value>
      </symbol>
      <symbol id="sm-52">
         <name>TIMG0_IRQHandler</name>
         <value>0xa25</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-53">
         <name>Flag_Stop</name>
         <value>0x202000f0</value>
         <object_component_ref idref="oc-7e"/>
      </symbol>
      <symbol id="sm-54">
         <name>PWMA</name>
         <value>0x202001dc</value>
      </symbol>
      <symbol id="sm-55">
         <name>PWMB</name>
         <value>0x202001e0</value>
      </symbol>
      <symbol id="sm-109">
         <name>SYSCFG_DL_init</name>
         <value>0x14c5</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-10a">
         <name>SYSCFG_DL_initPower</name>
         <value>0xfcd</value>
         <object_component_ref idref="oc-10e"/>
      </symbol>
      <symbol id="sm-10b">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x69d</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-10c">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x11e9</value>
         <object_component_ref idref="oc-110"/>
      </symbol>
      <symbol id="sm-10d">
         <name>SYSCFG_DL_PWM_0_init</name>
         <value>0xed1</value>
         <object_component_ref idref="oc-111"/>
      </symbol>
      <symbol id="sm-10e">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x1431</value>
         <object_component_ref idref="oc-112"/>
      </symbol>
      <symbol id="sm-10f">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x1095</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-110">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0x1b21</value>
         <object_component_ref idref="oc-114"/>
      </symbol>
      <symbol id="sm-111">
         <name>gPWM_0Backup</name>
         <value>0x20200108</value>
      </symbol>
      <symbol id="sm-11c">
         <name>Default_Handler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11d">
         <name>Reset_Handler</name>
         <value>0x1b8b</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-11e">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-11f">
         <name>NMI_Handler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-120">
         <name>HardFault_Handler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-121">
         <name>SVC_Handler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-122">
         <name>PendSV_Handler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-123">
         <name>SysTick_Handler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-124">
         <name>GROUP0_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-125">
         <name>TIMG8_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-126">
         <name>UART3_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-127">
         <name>ADC0_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-128">
         <name>ADC1_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-129">
         <name>CANFD0_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12a">
         <name>DAC0_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12b">
         <name>SPI0_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12c">
         <name>SPI1_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12d">
         <name>UART1_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12e">
         <name>UART2_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12f">
         <name>UART0_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-130">
         <name>TIMG6_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-131">
         <name>TIMA0_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-132">
         <name>TIMA1_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-133">
         <name>TIMG7_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-134">
         <name>TIMG12_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-135">
         <name>I2C0_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-136">
         <name>I2C1_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-137">
         <name>AES_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-138">
         <name>RTC_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-139">
         <name>DMA_IRQHandler</name>
         <value>0x1b83</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-14d">
         <name>fputc</name>
         <value>0x1549</value>
         <object_component_ref idref="oc-188"/>
      </symbol>
      <symbol id="sm-14e">
         <name>fputs</name>
         <value>0x114d</value>
         <object_component_ref idref="oc-183"/>
      </symbol>
      <symbol id="sm-163">
         <name>GROUP1_IRQHandler</name>
         <value>0x471</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-164">
         <name>gpio_interrup1</name>
         <value>0x202001ec</value>
      </symbol>
      <symbol id="sm-165">
         <name>gpio_interrup2</name>
         <value>0x202001f0</value>
      </symbol>
      <symbol id="sm-166">
         <name>Get_Encoder_countA</name>
         <value>0x202001d4</value>
      </symbol>
      <symbol id="sm-167">
         <name>Get_Encoder_countB</name>
         <value>0x202001d8</value>
      </symbol>
      <symbol id="sm-181">
         <name>click_N_Double</name>
         <value>0x345</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-182">
         <name>Key</name>
         <value>0x1499</value>
         <object_component_ref idref="oc-6f"/>
      </symbol>
      <symbol id="sm-19b">
         <name>LED_ON</name>
         <value>0x1a81</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-19c">
         <name>LED_Toggle</name>
         <value>0x1a95</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-19d">
         <name>LED_Flash</name>
         <value>0x12c1</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-1be">
         <name>limit_PWM</name>
         <value>0x1465</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-1bf">
         <name>Set_PWM</name>
         <value>0x591</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-1c0">
         <name>Velocity_A</name>
         <value>0xbb9</value>
         <object_component_ref idref="oc-74"/>
      </symbol>
      <symbol id="sm-1c1">
         <name>Velcity_Ki</name>
         <value>0x202000f4</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-1c2">
         <name>Velcity_Kp</name>
         <value>0x202000f8</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-1c3">
         <name>Velocity_B</name>
         <value>0xc7d</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c5">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c6">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c7">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c8">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c9">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1ca">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1cb">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1cc">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d5">
         <name>DL_Common_delayCycles</name>
         <value>0x1b5b</value>
         <object_component_ref idref="oc-137"/>
      </symbol>
      <symbol id="sm-1f4">
         <name>DL_Timer_setClockConfig</name>
         <value>0x17bd</value>
         <object_component_ref idref="oc-154"/>
      </symbol>
      <symbol id="sm-1f5">
         <name>DL_Timer_initTimerMode</name>
         <value>0x789</value>
         <object_component_ref idref="oc-15c"/>
      </symbol>
      <symbol id="sm-1f6">
         <name>DL_Timer_setCaptureCompareValue</name>
         <value>0x1b11</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-1f7">
         <name>DL_Timer_initPWMMode</name>
         <value>0xaf5</value>
         <object_component_ref idref="oc-194"/>
      </symbol>
      <symbol id="sm-1f8">
         <name>DL_Timer_setCaptureCompareOutCtl</name>
         <value>0x18e1</value>
         <object_component_ref idref="oc-156"/>
      </symbol>
      <symbol id="sm-1f9">
         <name>DL_Timer_setCaptCompUpdateMethod</name>
         <value>0x17a1</value>
         <object_component_ref idref="oc-157"/>
      </symbol>
      <symbol id="sm-1fa">
         <name>DL_TimerA_initPWMMode</name>
         <value>0xe51</value>
         <object_component_ref idref="oc-155"/>
      </symbol>
      <symbol id="sm-207">
         <name>DL_UART_init</name>
         <value>0x1235</value>
         <object_component_ref idref="oc-166"/>
      </symbol>
      <symbol id="sm-208">
         <name>DL_UART_setClockConfig</name>
         <value>0x1abb</value>
         <object_component_ref idref="oc-160"/>
      </symbol>
      <symbol id="sm-216">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x871</value>
         <object_component_ref idref="oc-149"/>
      </symbol>
      <symbol id="sm-217">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x127d</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-228">
         <name>printf</name>
         <value>0x10f1</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-23e">
         <name>__TI_printfi_minimal</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-172"/>
      </symbol>
      <symbol id="sm-24b">
         <name>_nop</name>
         <value>0x165f</value>
         <object_component_ref idref="oc-17a"/>
      </symbol>
      <symbol id="sm-24c">
         <name>_lock</name>
         <value>0x202000fc</value>
         <object_component_ref idref="oc-119"/>
      </symbol>
      <symbol id="sm-24d">
         <name>_unlock</name>
         <value>0x20200100</value>
         <object_component_ref idref="oc-11f"/>
      </symbol>
      <symbol id="sm-255">
         <name>_ftable</name>
         <value>0x20200000</value>
         <object_component_ref idref="oc-11b"/>
      </symbol>
      <symbol id="sm-25e">
         <name>memccpy</name>
         <value>0x163d</value>
         <object_component_ref idref="oc-199"/>
      </symbol>
      <symbol id="sm-26a">
         <name>_c_int00_noargs</name>
         <value>0x1615</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-26b">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-277">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1381</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-27f">
         <name>_system_pre_init</name>
         <value>0x1b8f</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-28a">
         <name>__TI_zero_init_nomemset</name>
         <value>0x19df</value>
         <object_component_ref idref="oc-50"/>
      </symbol>
      <symbol id="sm-293">
         <name>__TI_decompress_none</name>
         <value>0x1adf</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-29e">
         <name>__TI_decompress_lzss</name>
         <value>0xf51</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2aa">
         <name>abort</name>
         <value>0x1b7d</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-2b3">
         <name>HOSTexit</name>
         <value>0x1b87</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-2b4">
         <name>C$$EXIT</name>
         <value>0x1b86</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-2c4">
         <name>__aeabi_fadd</name>
         <value>0x957</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-2c5">
         <name>__addsf3</name>
         <value>0x957</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-2c6">
         <name>__aeabi_fsub</name>
         <value>0x94d</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-2c7">
         <name>__subsf3</name>
         <value>0x94d</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-2cd">
         <name>__muldsi3</name>
         <value>0x13bd</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-2d3">
         <name>__aeabi_fmul</name>
         <value>0xd41</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-2d4">
         <name>__mulsf3</name>
         <value>0xd41</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-2da">
         <name>__aeabi_f2iz</name>
         <value>0x13f9</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-2db">
         <name>__fixsfsi</name>
         <value>0x13f9</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-2e1">
         <name>__aeabi_i2f</name>
         <value>0x1345</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-2e2">
         <name>__floatsisf</name>
         <value>0x1345</value>
         <object_component_ref idref="oc-a3"/>
      </symbol>
      <symbol id="sm-2e8">
         <name>__aeabi_memcpy</name>
         <value>0x1b65</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2e9">
         <name>__aeabi_memcpy4</name>
         <value>0x1b65</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2ea">
         <name>__aeabi_memcpy8</name>
         <value>0x1b65</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2f1">
         <name>__aeabi_memset</name>
         <value>0x1b31</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-2f2">
         <name>__aeabi_memset4</name>
         <value>0x1b31</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-2f3">
         <name>__aeabi_memset8</name>
         <value>0x1b31</value>
         <object_component_ref idref="oc-198"/>
      </symbol>
      <symbol id="sm-2f9">
         <name>__aeabi_uidiv</name>
         <value>0x1305</value>
         <object_component_ref idref="oc-19e"/>
      </symbol>
      <symbol id="sm-2fa">
         <name>__aeabi_uidivmod</name>
         <value>0x1305</value>
         <object_component_ref idref="oc-19e"/>
      </symbol>
      <symbol id="sm-305">
         <name>__aeabi_idiv0</name>
         <value>0x13f7</value>
         <object_component_ref idref="oc-1c9"/>
      </symbol>
      <symbol id="sm-30e">
         <name>TI_memcpy_small</name>
         <value>0x1acd</value>
         <object_component_ref idref="oc-c3"/>
      </symbol>
      <symbol id="sm-317">
         <name>TI_memset_small</name>
         <value>0x1b4d</value>
         <object_component_ref idref="oc-108"/>
      </symbol>
      <symbol id="sm-318">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-31b">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-31c">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
