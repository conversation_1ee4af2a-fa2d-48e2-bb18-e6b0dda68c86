******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Mon Jul 28 10:24:41 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001615


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001c70  0001e390  R  X
  SRAM                  20200000   00008000  000003f9  00007c07  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001c70   00001c70    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001ad8   00001ad8    r-x .text
  00001b98    00001b98    00000078   00000078    r-- .rodata
  00001c10    00001c10    00000060   00000060    r-- .cinit
20200000    20200000    000001fc   00000000    rw-
  20200000    20200000    00000105   00000000    rw- .data
  20200108    20200108    000000f4   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001ad8     
                  000000c0    00000284     libc.a : _printfi.c.obj (.text:__TI_printfi_minimal)
                  00000344    0000012c     key.o (.text.click_N_Double)
                  00000470    00000120     encoder.o (.text.GROUP1_IRQHandler)
                  00000590    0000010c     motor.o (.text.Set_PWM)
                  0000069c    000000ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000788    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00000870    000000dc                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  0000094c    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00000a24    000000d0     empty.o (.text.TIMG0_IRQHandler)
                  00000af4    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00000bb8    000000c4     motor.o (.text.Velocity_A)
                  00000c7c    000000c4     motor.o (.text.Velocity_B)
                  00000d40    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00000dcc    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00000e50    00000080     driverlib.a : dl_timer.o (.text.DL_TimerA_initPWMMode)
                  00000ed0    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00000f50    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000fcc    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001034    00000060     empty.o (.text.main)
                  00001094    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000010f0    0000005c     libc.a : printf.c.obj (.text.printf)
                  0000114c    00000050     board.o (.text.fputs)
                  0000119c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000011e8    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00001234    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000127c    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000012c0    00000044     led.o (.text.LED_Flash)
                  00001304    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001344    0000003c                            : floatsisf.S.obj (.text.__floatsisf)
                  00001380    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000013bc    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000013f6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000013f8    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00001430    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00001464    00000034     motor.o (.text.limit_PWM)
                  00001498    0000002c     key.o (.text.Key)
                  000014c4    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000014f0    0000002c     empty.o (.text.__NVIC_ClearPendingIRQ)
                  0000151c    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  00001548    0000002c     board.o (.text.fputc)
                  00001574    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000159c    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  000015c4    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  000015ec    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00001614    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000163c    00000022            : memccpy.c.obj (.text.memccpy)
                  0000165e    00000002            : _lock.c.obj (.text._nop)
                  00001660    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001680    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000016a0    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000016be    00000002     --HOLE-- [fill = 0]
                  000016c0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000016dc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000016f8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00001714    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInput)
                  00001730    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000174c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00001768    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00001784    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000017a0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000017bc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000017d8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000017f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001808    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00001820    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00001838    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00001850    00000018     motor.o (.text.DL_GPIO_setPins)
                  00001868    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00001880    00000018     led.o (.text.DL_GPIO_togglePins)
                  00001898    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000018b0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000018c8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000018e0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000018f8    00000018     empty.o (.text.DL_Timer_startCounter)
                  00001910    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00001928    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001940    00000018     board.o (.text.DL_UART_isBusy)
                  00001958    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001970    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00001986    00000016     key.o (.text.DL_GPIO_readPins)
                  0000199c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000019b2    00000016     ti_msp_dl_config.o (.text.DL_UART_enableLoopbackMode)
                  000019c8    00000016     board.o (.text.DL_UART_transmitData)
                  000019de    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000019f4    00000014     led.o (.text.DL_GPIO_clearPins)
                  00001a08    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00001a1c    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00001a30    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001a44    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00001a58    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00001a6c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00001a80    00000014     led.o (.text.LED_ON)
                  00001a94    00000014     led.o (.text.LED_Toggle)
                  00001aa8    00000012     empty.o (.text.DL_Timer_getPendingInterrupt)
                  00001aba    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00001acc    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00001ade    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00001af0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001b00    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00001b10    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00001b20    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00001b30    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00001b3e    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00001b4c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00001b5a    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001b64    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001b6c    00000008     libc.a : printf.c.obj (.text._outc)
                  00001b74    00000008            : printf.c.obj (.text._outs)
                  00001b7c    00000006            : exit.c.obj (.text:abort)
                  00001b82    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001b86    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00001b8a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001b8e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001b92    00000006     --HOLE-- [fill = 0]

.cinit     0    00001c10    00000060     
                  00001c10    00000036     (.cinit..data.load) [load image, compression = lzss]
                  00001c46    00000002     --HOLE-- [fill = 0]
                  00001c48    0000000c     (__TI_handler_table)
                  00001c54    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001c5c    00000010     (__TI_cinit_table)
                  00001c6c    00000004     --HOLE-- [fill = 0]

.rodata    0    00001b98    00000078     
                  00001b98    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00001bc0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00001bd4    00000011     libc.a : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00001be5    00000001     --HOLE-- [fill = 0]
                  00001be6    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00001bf0    00000009     empty.o (.rodata.str1.9517790425240694019.1)
                  00001bf9    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00001bfc    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00001c04    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00001c07    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00001c09    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000105     UNINITIALIZED
                  20200000    000000f0     libc.a : defs.c.obj (.data._ftable)
                  202000f0    00000004     empty.o (.data.Flag_Stop)
                  202000f4    00000004     motor.o (.data.Velcity_Ki)
                  202000f8    00000004     motor.o (.data.Velcity_Kp)
                  202000fc    00000004     libc.a : _lock.c.obj (.data._lock)
                  20200100    00000004            : _lock.c.obj (.data._unlock)
                  20200104    00000001     key.o (.data.click_N_Double.double_key)

.bss       0    20200108    000000f4     UNINITIALIZED
                  20200108    000000bc     (.common:gPWM_0Backup)
                  202001c4    00000004     motor.o (.bss.Velocity_A.ControlVelocityA)
                  202001c8    00000004     motor.o (.bss.Velocity_A.Last_biasA)
                  202001cc    00000004     motor.o (.bss.Velocity_B.ControlVelocityB)
                  202001d0    00000004     motor.o (.bss.Velocity_B.Last_biasB)
                  202001d4    00000004     (.common:Get_Encoder_countA)
                  202001d8    00000004     (.common:Get_Encoder_countB)
                  202001dc    00000004     (.common:PWMA)
                  202001e0    00000004     (.common:PWMB)
                  202001e4    00000004     (.common:encoderA_cnt)
                  202001e8    00000004     (.common:encoderB_cnt)
                  202001ec    00000004     (.common:gpio_interrup1)
                  202001f0    00000004     (.common:gpio_interrup2)
                  202001f4    00000002     led.o (.bss.LED_Flash.temp)
                  202001f6    00000002     key.o (.bss.click_N_Double.Forever_count)
                  202001f8    00000002     key.o (.bss.click_N_Double.count_single)
                  202001fa    00000001     key.o (.bss.click_N_Double.count_key)
                  202001fb    00000001     key.o (.bss.click_N_Double.flag_key)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             1846   86        188    
       empty.o                        434    9         20     
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         2288   287       208    
                                                              
    .\Hardware\
       motor.o                        756    0         24     
       encoder.o                      362    0         16     
       key.o                          366    0         7      
       board.o                        170    0         0      
       led.o                          152    0         2      
    +--+------------------------------+------+---------+---------+
       Total:                         1806   0         49     
                                                              
    D:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     652    0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288    0         0      
       dl_uart.o                      90     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1040   0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 658    17        0      
       defs.c.obj                     0      0         240    
       copy_decompress_lzss.c.obj     124    0         0      
       printf.c.obj                   108    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       memccpy.c.obj                  34     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       memset16.S.obj                 14     0         0      
       _lock.c.obj                    2      0         8      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1108   17        248    
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memset.S.obj             14     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         618    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      90        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   6864   394       1017   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001c5c records: 2, size/record: 8, table size: 16
	.data: load addr=00001c10, load size=00000036 bytes, run addr=20200000, run size=00000105 bytes, compression=lzss
	.bss: load addr=00001c54, load size=00000008 bytes, run addr=20200108, run size=000000f4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001c48 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00001b83  ADC0_IRQHandler                      
00001b83  ADC1_IRQHandler                      
00001b83  AES_IRQHandler                       
00001b86  C$$EXIT                              
00001b83  CANFD0_IRQHandler                    
00001b83  DAC0_IRQHandler                      
00001b5b  DL_Common_delayCycles                
00000871  DL_SYSCTL_configSYSPLL               
0000127d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00000e51  DL_TimerA_initPWMMode                
00000af5  DL_Timer_initPWMMode                 
00000789  DL_Timer_initTimerMode               
000017a1  DL_Timer_setCaptCompUpdateMethod     
000018e1  DL_Timer_setCaptureCompareOutCtl     
00001b11  DL_Timer_setCaptureCompareValue      
000017bd  DL_Timer_setClockConfig              
00001235  DL_UART_init                         
00001abb  DL_UART_setClockConfig               
00001b83  DMA_IRQHandler                       
00001b83  Default_Handler                      
202000f0  Flag_Stop                            
00001b83  GROUP0_IRQHandler                    
00000471  GROUP1_IRQHandler                    
202001d4  Get_Encoder_countA                   
202001d8  Get_Encoder_countB                   
00001b87  HOSTexit                             
00001b83  HardFault_Handler                    
00001b83  I2C0_IRQHandler                      
00001b83  I2C1_IRQHandler                      
00001499  Key                                  
000012c1  LED_Flash                            
00001a81  LED_ON                               
00001a95  LED_Toggle                           
00001b83  NMI_Handler                          
202001dc  PWMA                                 
202001e0  PWMB                                 
00001b83  PendSV_Handler                       
00001b83  RTC_IRQHandler                       
00001b8b  Reset_Handler                        
00001b83  SPI0_IRQHandler                      
00001b83  SPI1_IRQHandler                      
00001b83  SVC_Handler                          
0000069d  SYSCFG_DL_GPIO_init                  
00000ed1  SYSCFG_DL_PWM_0_init                 
000011e9  SYSCFG_DL_SYSCTL_init                
00001b21  SYSCFG_DL_SYSTICK_init               
00001431  SYSCFG_DL_TIMER_0_init               
00001095  SYSCFG_DL_UART_0_init                
000014c5  SYSCFG_DL_init                       
00000fcd  SYSCFG_DL_initPower                  
00000591  Set_PWM                              
00001b83  SysTick_Handler                      
00001b83  TIMA0_IRQHandler                     
00001b83  TIMA1_IRQHandler                     
00000a25  TIMG0_IRQHandler                     
00001b83  TIMG12_IRQHandler                    
00001b83  TIMG6_IRQHandler                     
00001b83  TIMG7_IRQHandler                     
00001b83  TIMG8_IRQHandler                     
00001acd  TI_memcpy_small                      
00001b4d  TI_memset_small                      
00001b83  UART0_IRQHandler                     
00001b83  UART1_IRQHandler                     
00001b83  UART2_IRQHandler                     
00001b83  UART3_IRQHandler                     
202000f4  Velcity_Ki                           
202000f8  Velcity_Kp                           
00000bb9  Velocity_A                           
00000c7d  Velocity_B                           
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00001c5c  __TI_CINIT_Base                      
00001c6c  __TI_CINIT_Limit                     
00001c6c  __TI_CINIT_Warm                      
00001c48  __TI_Handler_Table_Base              
00001c54  __TI_Handler_Table_Limit             
00001381  __TI_auto_init_nobinit_nopinit       
00000f51  __TI_decompress_lzss                 
00001adf  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi_minimal                 
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000019df  __TI_zero_init_nomemset              
00000957  __addsf3                             
000013f9  __aeabi_f2iz                         
00000957  __aeabi_fadd                         
00000d41  __aeabi_fmul                         
0000094d  __aeabi_fsub                         
00001345  __aeabi_i2f                          
000013f7  __aeabi_idiv0                        
00001b65  __aeabi_memcpy                       
00001b65  __aeabi_memcpy4                      
00001b65  __aeabi_memcpy8                      
00001b31  __aeabi_memset                       
00001b31  __aeabi_memset4                      
00001b31  __aeabi_memset8                      
00001305  __aeabi_uidiv                        
00001305  __aeabi_uidivmod                     
ffffffff  __binit__                            
000013f9  __fixsfsi                            
00001345  __floatsisf                          
UNDEFED   __mpu_init                           
000013bd  __muldsi3                            
00000d41  __mulsf3                             
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
0000094d  __subsf3                             
00001615  _c_int00_noargs                      
20200000  _ftable                              
202000fc  _lock                                
0000165f  _nop                                 
UNDEFED   _system_post_cinit                   
00001b8f  _system_pre_init                     
20200100  _unlock                              
00001b7d  abort                                
ffffffff  binit                                
00000345  click_N_Double                       
202001e4  encoderA_cnt                         
202001e8  encoderB_cnt                         
00001549  fputc                                
0000114d  fputs                                
20200108  gPWM_0Backup                         
202001ec  gpio_interrup1                       
202001f0  gpio_interrup2                       
00000000  interruptVectors                     
00001465  limit_PWM                            
00001035  main                                 
0000163d  memccpy                              
000010f1  printf                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi_minimal                 
00000200  __STACK_SIZE                         
00000345  click_N_Double                       
00000471  GROUP1_IRQHandler                    
00000591  Set_PWM                              
0000069d  SYSCFG_DL_GPIO_init                  
00000789  DL_Timer_initTimerMode               
00000871  DL_SYSCTL_configSYSPLL               
0000094d  __aeabi_fsub                         
0000094d  __subsf3                             
00000957  __addsf3                             
00000957  __aeabi_fadd                         
00000a25  TIMG0_IRQHandler                     
00000af5  DL_Timer_initPWMMode                 
00000bb9  Velocity_A                           
00000c7d  Velocity_B                           
00000d41  __aeabi_fmul                         
00000d41  __mulsf3                             
00000e51  DL_TimerA_initPWMMode                
00000ed1  SYSCFG_DL_PWM_0_init                 
00000f51  __TI_decompress_lzss                 
00000fcd  SYSCFG_DL_initPower                  
00001035  main                                 
00001095  SYSCFG_DL_UART_0_init                
000010f1  printf                               
0000114d  fputs                                
000011e9  SYSCFG_DL_SYSCTL_init                
00001235  DL_UART_init                         
0000127d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000012c1  LED_Flash                            
00001305  __aeabi_uidiv                        
00001305  __aeabi_uidivmod                     
00001345  __aeabi_i2f                          
00001345  __floatsisf                          
00001381  __TI_auto_init_nobinit_nopinit       
000013bd  __muldsi3                            
000013f7  __aeabi_idiv0                        
000013f9  __aeabi_f2iz                         
000013f9  __fixsfsi                            
00001431  SYSCFG_DL_TIMER_0_init               
00001465  limit_PWM                            
00001499  Key                                  
000014c5  SYSCFG_DL_init                       
00001549  fputc                                
00001615  _c_int00_noargs                      
0000163d  memccpy                              
0000165f  _nop                                 
000017a1  DL_Timer_setCaptCompUpdateMethod     
000017bd  DL_Timer_setClockConfig              
000018e1  DL_Timer_setCaptureCompareOutCtl     
000019df  __TI_zero_init_nomemset              
00001a81  LED_ON                               
00001a95  LED_Toggle                           
00001abb  DL_UART_setClockConfig               
00001acd  TI_memcpy_small                      
00001adf  __TI_decompress_none                 
00001b11  DL_Timer_setCaptureCompareValue      
00001b21  SYSCFG_DL_SYSTICK_init               
00001b31  __aeabi_memset                       
00001b31  __aeabi_memset4                      
00001b31  __aeabi_memset8                      
00001b4d  TI_memset_small                      
00001b5b  DL_Common_delayCycles                
00001b65  __aeabi_memcpy                       
00001b65  __aeabi_memcpy4                      
00001b65  __aeabi_memcpy8                      
00001b7d  abort                                
00001b83  ADC0_IRQHandler                      
00001b83  ADC1_IRQHandler                      
00001b83  AES_IRQHandler                       
00001b83  CANFD0_IRQHandler                    
00001b83  DAC0_IRQHandler                      
00001b83  DMA_IRQHandler                       
00001b83  Default_Handler                      
00001b83  GROUP0_IRQHandler                    
00001b83  HardFault_Handler                    
00001b83  I2C0_IRQHandler                      
00001b83  I2C1_IRQHandler                      
00001b83  NMI_Handler                          
00001b83  PendSV_Handler                       
00001b83  RTC_IRQHandler                       
00001b83  SPI0_IRQHandler                      
00001b83  SPI1_IRQHandler                      
00001b83  SVC_Handler                          
00001b83  SysTick_Handler                      
00001b83  TIMA0_IRQHandler                     
00001b83  TIMA1_IRQHandler                     
00001b83  TIMG12_IRQHandler                    
00001b83  TIMG6_IRQHandler                     
00001b83  TIMG7_IRQHandler                     
00001b83  TIMG8_IRQHandler                     
00001b83  UART0_IRQHandler                     
00001b83  UART1_IRQHandler                     
00001b83  UART2_IRQHandler                     
00001b83  UART3_IRQHandler                     
00001b86  C$$EXIT                              
00001b87  HOSTexit                             
00001b8b  Reset_Handler                        
00001b8f  _system_pre_init                     
00001c48  __TI_Handler_Table_Base              
00001c54  __TI_Handler_Table_Limit             
00001c5c  __TI_CINIT_Base                      
00001c6c  __TI_CINIT_Limit                     
00001c6c  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  _ftable                              
202000f0  Flag_Stop                            
202000f4  Velcity_Ki                           
202000f8  Velcity_Kp                           
202000fc  _lock                                
20200100  _unlock                              
20200108  gPWM_0Backup                         
202001d4  Get_Encoder_countA                   
202001d8  Get_Encoder_countB                   
202001dc  PWMA                                 
202001e0  PWMB                                 
202001e4  encoderA_cnt                         
202001e8  encoderB_cnt                         
202001ec  gpio_interrup1                       
202001f0  gpio_interrup2                       
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[143 symbols]
